/* Tour Builder Luxury Enhancements */

/* Custom slider styling for luxury appearance */
.tour-builder-slider .slider-track {
  background: linear-gradient(90deg, 
    rgba(212, 194, 164, 0.2) 0%, 
    rgba(212, 194, 164, 0.4) 50%, 
    rgba(212, 194, 164, 0.2) 100%);
  height: 6px;
  border-radius: 3px;
}

.tour-builder-slider .slider-thumb {
  background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 100%);
  border: 2px solid rgba(242, 238, 230, 0.3);
  box-shadow: 0 4px 12px rgba(212, 194, 164, 0.4);
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.tour-builder-slider .slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(212, 194, 164, 0.6);
}

/* Luxury form section animations */
@keyframes luxuryFadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.luxury-form-section {
  animation: luxuryFadeInUp 0.6s ease-out;
}

/* Enhanced luxury glass container for tour builder */
.tour-builder-glass {
  background: linear-gradient(135deg, 
    rgba(212, 194, 164, 0.08) 0%,
    rgba(212, 194, 164, 0.05) 50%,
    rgba(212, 194, 164, 0.12) 100%);
  backdrop-filter: blur(30px) saturate(200%);
  -webkit-backdrop-filter: blur(30px) saturate(200%);
  border: 1px solid rgba(212, 194, 164, 0.25);
  box-shadow: 
    0 32px 64px rgba(22, 25, 29, 0.5),
    0 0 0 1px rgba(212, 194, 164, 0.1),
    inset 0 1px 0 rgba(212, 194, 164, 0.2),
    inset 0 -1px 0 rgba(212, 194, 164, 0.1);
  position: relative;
  overflow: hidden;
}

.tour-builder-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.6), 
    transparent);
  animation: luxuryShimmer 4s ease-in-out infinite;
}

.tour-builder-glass::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, 
    rgba(212, 194, 164, 0.08) 0%, 
    transparent 60%);
  pointer-events: none;
}

/* Luxury button selection states */
.luxury-selection-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.luxury-selection-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 194, 164, 0.2), 
    transparent);
  transition: left 0.6s ease;
}

.luxury-selection-button:hover::before {
  left: 100%;
}

.luxury-selection-button.selected {
  background: linear-gradient(135deg, 
    rgba(212, 194, 164, 0.2) 0%, 
    rgba(212, 194, 164, 0.15) 50%, 
    rgba(212, 194, 164, 0.25) 100%);
  border-color: rgba(212, 194, 164, 0.8);
  box-shadow: 
    0 8px 25px rgba(212, 194, 164, 0.3),
    inset 0 1px 0 rgba(212, 194, 164, 0.3);
}

/* Luxury separator styling */
.luxury-separator {
  position: relative;
}

.luxury-separator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(212, 194, 164, 0.3) 20%, 
    rgba(212, 194, 164, 0.6) 50%, 
    rgba(212, 194, 164, 0.3) 80%, 
    transparent 100%);
  transform: translateY(-50%);
}

/* Premium input styling */
.luxury-input {
  background: rgba(242, 238, 230, 0.05);
  border: 1px solid rgba(212, 194, 164, 0.3);
  color: #F2EEE6;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.luxury-input:focus {
  background: rgba(242, 238, 230, 0.08);
  border-color: rgba(212, 194, 164, 0.8);
  box-shadow: 
    0 0 0 3px rgba(212, 194, 164, 0.2),
    0 8px 25px rgba(212, 194, 164, 0.15);
  outline: none;
}

.luxury-input::placeholder {
  color: rgba(242, 238, 230, 0.5);
}

/* Luxury submit button */
.luxury-submit-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 50%, #D4C2A4 100%);
  background-size: 200% 100%;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 
    0 8px 25px rgba(212, 194, 164, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.luxury-submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  transition: left 0.8s ease;
}

.luxury-submit-button:hover {
  background-position: 100% 0;
  transform: translateY(-2px);
  box-shadow: 
    0 12px 35px rgba(212, 194, 164, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.luxury-submit-button:hover::before {
  left: 100%;
}

.luxury-submit-button:active {
  transform: translateY(0);
}

/* Premium shimmer animation */
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Luxury glow text effect */
.luxury-glow-text {
  text-shadow: 
    0 0 10px rgba(212, 194, 164, 0.3),
    0 0 20px rgba(212, 194, 164, 0.2),
    0 0 30px rgba(212, 194, 164, 0.1);
}

/* Responsive luxury enhancements */
@media (max-width: 768px) {
  .tour-builder-glass {
    backdrop-filter: blur(20px) saturate(150%);
    -webkit-backdrop-filter: blur(20px) saturate(150%);
  }
  
  .luxury-submit-button {
    background-size: 150% 100%;
  }
  
  .luxury-glow-text {
    text-shadow: 0 0 8px rgba(212, 194, 164, 0.3);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tour-builder-glass {
    background: rgba(212, 194, 164, 0.15);
    border-color: rgba(212, 194, 164, 0.6);
  }
  
  .luxury-input {
    background: rgba(242, 238, 230, 0.1);
    border-color: rgba(212, 194, 164, 0.6);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .luxury-form-section,
  .tour-builder-glass::before,
  .luxury-selection-button::before,
  .luxury-submit-button::before {
    animation: none;
  }
  
  .luxury-submit-button:hover {
    transform: none;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .luxury-selection-button:hover {
    transform: scale(0.98);
  }
  
  .luxury-submit-button:hover {
    transform: scale(0.98);
  }
}

/* Custom scrollbar for destination list */
.destinations-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.destinations-scrollbar::-webkit-scrollbar-track {
  background: rgba(212, 194, 164, 0.1);
  border-radius: 3px;
}

.destinations-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(212, 194, 164, 0.4);
  border-radius: 3px;
}

.destinations-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 194, 164, 0.6);
}

/* Calendar luxury styling overrides */
.tour-builder-calendar {
  background: #16191D;
  border: 1px solid rgba(212, 194, 164, 0.3);
}

.tour-builder-calendar .rdp {
  --rdp-cell-size: 36px;
  --rdp-accent-color: #D4C2A4;
  --rdp-background-color: #16191D;
  margin: 0;
}

.tour-builder-calendar .rdp-button {
  border: none;
  background: transparent;
  color: #F2EEE6;
  font-size: 14px;
  transition: all 0.2s ease;
}

.tour-builder-calendar .rdp-button:hover {
  background-color: rgba(212, 194, 164, 0.2);
  color: #F2EEE6;
}

.tour-builder-calendar .rdp-button:focus {
  background-color: rgba(212, 194, 164, 0.2);
  color: #F2EEE6;
  outline: none;
}

.tour-builder-calendar .rdp-day_selected {
  background-color: #D4C2A4 !important;
  color: #16191D !important;
  font-weight: 600;
}

.tour-builder-calendar .rdp-day_today {
  background-color: rgba(212, 194, 164, 0.3);
  color: #F2EEE6;
  border: 1px solid rgba(212, 194, 164, 0.5);
}

.tour-builder-calendar .rdp-day_outside {
  color: rgba(242, 238, 230, 0.3);
}

.tour-builder-calendar .rdp-day_disabled {
  color: rgba(242, 238, 230, 0.2);
  cursor: not-allowed;
}

.tour-builder-calendar .rdp-head_cell {
  color: #D4C2A4;
  font-weight: 500;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tour-builder-calendar .rdp-caption_label {
  color: #F2EEE6;
  font-weight: 600;
  font-size: 16px;
}

.tour-builder-calendar .rdp-nav_button {
  color: #F2EEE6;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.tour-builder-calendar .rdp-nav_button:hover {
  background-color: rgba(212, 194, 164, 0.2);
  color: #D4C2A4;
}

/* Ensure calendar text is always visible */
.tour-builder-calendar * {
  color: inherit;
}

.tour-builder-calendar .rdp-day {
  color: #F2EEE6 !important;
}

.tour-builder-calendar .rdp-day:hover {
  color: #F2EEE6 !important;
}

.tour-builder-calendar .rdp-day_selected {
  color: #16191D !important;
}
